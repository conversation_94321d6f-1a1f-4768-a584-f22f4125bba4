/* <PERSON><PERSON><PERSON><PERSON> Globais - Design Moderno 2025 */
:root {
    /* <PERSON><PERSON> principais - Paleta moderna */
    --primary-color: #4361ee;
    --primary-dark: #3a0ca3;
    --primary-light: #7209b7;
    --secondary-color: #4cc9f0;
    --secondary-dark: #4895ef;
    --accent-color: #f72585;
    --accent-light: #ff758f;

    /* Cores de estado */
    --success-color: #06d6a0;
    --success-light: #a7f3d0;
    --warning-color: #ffd166;
    --warning-light: #fef3c7;
    --danger-color: #ef476f;
    --danger-light: #fecdd3;
    --info-color: #118ab2;
    --info-light: #bae6fd;

    /* Cores neutras */
    --light-color: #f8fafc;
    --light-gray: #f1f5f9;
    --medium-gray: #e2e8f0;
    --dark-gray: #64748b;
    --dark-color: #334155;
    --text-color: #1e293b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;

    /* Efeitos */
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --card-shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --button-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition-speed: 0.3s;
    --transition-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);

    /* Arredondamentos */
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;
}

/* Estilos base */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    padding-top: 4.5rem;
    padding-bottom: 3rem;
    line-height: 1.6;
    font-size: 16px;
    letter-spacing: 0.01em;
}

/* Tipografia moderna */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    letter-spacing: -0.025em;
}

h2 {
    font-size: 2rem;
    letter-spacing: -0.025em;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1.5rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-speed);
}

a:hover {
    color: var(--primary-dark);
}

/* Navbar moderna */
.navbar {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    padding: 0.75rem 1rem;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.25rem;
    color: white !important;
    letter-spacing: 0.05em;
    position: relative;
    padding-bottom: 2px;
}

.navbar-brand:after {
    content: '';
    position: absolute;
    width: 30%;
    height: 3px;
    bottom: -2px;
    left: 0;
    background-color: var(--accent-color);
    border-radius: var(--border-radius-full);
    transition: width 0.3s var(--transition-bounce);
}

.navbar-brand:hover:after {
    width: 100%;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    position: relative;
    z-index: 1;
}

.navbar-dark .navbar-nav .nav-link:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius);
    z-index: -1;
    transition: height 0.2s var(--transition-bounce);
}

.navbar-dark .navbar-nav .nav-link:hover:before,
.navbar-dark .navbar-nav .nav-link:focus:before,
.navbar-dark .navbar-nav .active > .nav-link:before {
    height: 100%;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .active > .nav-link {
    color: white;
}

/* Cards modernos com efeitos */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: all 0.4s var(--transition-bounce);
    margin-bottom: 1.5rem;
    overflow: hidden;
    background-color: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    border-top-left-radius: var(--border-radius-lg) !important;
    border-top-right-radius: var(--border-radius-lg) !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
}

.card-header h4 {
    margin-bottom: 0;
    font-weight: 700;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: white;
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    border-bottom-left-radius: var(--border-radius-lg) !important;
    border-bottom-right-radius: var(--border-radius-lg) !important;
}

/* Botões modernos com efeitos */
.btn {
    font-weight: 600;
    padding: 0.625rem 1.25rem;
    border-radius: var(--border-radius);
    transition: all 0.3s var(--transition-bounce);
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: none;
    box-shadow: var(--button-shadow);
}

.btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    z-index: -1;
}

.btn:hover:after, .btn:focus:after {
    width: 300%;
    height: 300%;
}

.btn:active {
    transform: translateY(2px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.4);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover, .btn-success:focus {
    background-color: #05b589;
    box-shadow: 0 4px 12px rgba(6, 214, 160, 0.4);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Formulários modernos */
.form-control {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background-color: var(--light-gray);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    background-color: white;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.input-group-text {
    border: none;
    background-color: var(--light-gray);
    color: var(--dark-gray);
    padding: 0.75rem 1rem;
}

/* Alertas modernos */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    border-left: 4px solid transparent;
    display: flex;
    align-items: center;
}

.alert i {
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

.alert-info {
    background-color: var(--info-light);
    color: var(--info-color);
    border-left-color: var(--info-color);
}

.alert-success {
    background-color: var(--success-light);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-warning {
    background-color: var(--warning-light);
    color: #b45309;
    border-left-color: var(--warning-color);
}

.alert-danger {
    background-color: var(--danger-light);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

/* Tabelas modernas */
.table {
    color: var(--text-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 0 0 1px var(--border-color);
    margin-bottom: 1.5rem;
}

.table thead th {
    background-color: var(--light-gray);
    color: var(--dark-color);
    font-weight: 700;
    border-top: none;
    border-bottom: 2px solid var(--border-color);
    padding: 1rem;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table-hover tbody tr {
    transition: all var(--transition-speed);
}

.table-hover tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--light-gray);
}

/* Badges modernos */
.badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: var(--border-radius-full);
    font-size: 0.75rem;
    letter-spacing: 0.025em;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: #92400e;
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.badge-info {
    background-color: var(--info-color);
    color: white;
}

/* Utilitários */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #04a57c 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
}

.bg-light {
    background-color: var(--light-gray) !important;
}

/* Animações e efeitos */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideInRight 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Elementos especiais */
.feature-icon {
    transition: all 0.3s var(--transition-bounce);
}

.card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Calendário e agendamentos */
.calendar-day {
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin: 0.25rem;
    transition: all var(--transition-speed);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.calendar-day:hover {
    background-color: var(--light-gray);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.calendar-day.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.time-slot {
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
    border-radius: var(--border-radius);
    background-color: var(--light-gray);
    transition: all var(--transition-speed);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.time-slot:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.time-slot.booked {
    background-color: var(--medium-gray);
    color: var(--dark-gray);
    cursor: not-allowed;
}

/* Footer */
footer {
    background-color: var(--light-gray);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsividade */
@media (max-width: 992px) {
    h1 {
        font-size: 2.25rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 4rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .card-header, .card-body, .card-footer {
        padding: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    .table thead th, .table tbody td {
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .btn {
        padding: 0.5rem 1rem;
    }

    .btn-lg {
        padding: 0.625rem 1.25rem;
    }

    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .navbar-brand:after {
        display: none;
    }
}
