{% extends "base.html" %}

{% block title %}Agendar Consulta - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Agendar Consulta</h4>
                <a href="{{ url_for('main.patient_view_professional_schedule', id=professional.id) }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h5>Detalhes da Consulta</h5>
                    <p class="mb-1"><strong>Profissional:</strong> {{ professional.name }} ({{ professional.specialty }})</p>
                    <p class="mb-1"><strong>Data:</strong> {{ date_str }}</p>
                    <p class="mb-0"><strong><PERSON><PERSON><PERSON><PERSON>:</strong> {{ time_slot }}</p>
                </div>
                
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    {{ form.appointment_date() }}
                    {{ form.start_time() }}
                    {{ form.end_time() }}
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows=3, placeholder="Informe qualquer detalhe importante sobre sua consulta...") }}
                        {% for error in form.notes.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <div class="alert alert-warning mb-0">
                    <strong>Importante:</strong> Ao confirmar o agendamento, você está se comprometendo a comparecer na data e horário marcados. Caso precise cancelar, faça-o com pelo menos 24 horas de antecedência.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
