from flask import render_template, flash, redirect, url_for, request, Blueprint, abort, jsonify, session
from flask_login import login_user, logout_user, current_user, login_required
from app.models import User, Professional, Patient, Schedule, Appointment, get_db
from app.google_calendar import oauth2callback as google_oauth2callback
from app.forms import (
    LoginForm, RegistrationForm, ProfessionalForm, PatientForm, ScheduleForm,
    AppointmentForm, AppointmentSearchForm, PatientRegistrationForm, PatientAppointmentForm,
    PostAppointmentNotesForm, AppointmentFilterForm
)
from urllib.parse import urlparse
from datetime import datetime, date, timedelta

main = Blueprint('main', __name__)

@main.route('/')
@main.route('/index')
def index():
    return render_template('index.html', title='Home')

@main.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.get_by_username(form.username.data)
        if user is None or not user.check_password(form.password.data):
            flash('Invalid username or password')
            return redirect(url_for('main.login'))
        login_user(user)
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            if user.is_admin():
                next_page = url_for('main.admin_dashboard')
            elif user.is_professional():
                next_page = url_for('main.professional_dashboard')
            elif user.is_patient():
                next_page = url_for('main.patient_dashboard')
        return redirect(next_page)
    return render_template('login.html', title='Sign In', form=form)

@main.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('main.index'))


@main.route('/oauth2callback')
def oauth2callback():
    result = google_oauth2callback()

    # Verificar se há dados do formulário de agendamento na sessão
    appointment_form_data = session.get('appointment_form_data')
    if appointment_form_data:
        try:
            from app.google_calendar import create_google_meet_event
            from datetime import datetime, timedelta

            # Recuperar informações do agendamento
            professional_id = appointment_form_data.get('professional_id')
            patient_id = appointment_form_data.get('patient_id')
            appointment_date = appointment_form_data.get('appointment_date')
            start_time = appointment_form_data.get('start_time')
            end_time = appointment_form_data.get('end_time')
            notes = appointment_form_data.get('notes')

            # Obter informações do profissional e paciente
            professional = Professional.get_by_id(professional_id)
            patient = Patient.get_by_id(patient_id)

            if professional and patient:
                # Converter a data e hora para objetos datetime
                appointment_datetime = datetime.strptime(f"{appointment_date} {start_time}", "%Y-%m-%d %H:%M")

                # Definir o horário de término
                end_datetime = datetime.strptime(f"{appointment_date} {end_time}", "%Y-%m-%d %H:%M")

                # Criar o evento no Google Calendar
                summary = f"Consulta: {professional.name} - {patient.name}"
                description = f"Consulta agendada entre {professional.name} e {patient.name}"

                # Lista de e-mails dos participantes
                attendees = []
                if patient.email:
                    attendees.append(patient.email)

                # Criar o evento e obter o link do Google Meet
                meet_link = create_google_meet_event(
                    summary=summary,
                    description=description,
                    start_time=appointment_datetime,
                    end_time=end_datetime,
                    attendees=attendees
                )

                if meet_link:
                    # Criar o agendamento com o link do Google Meet
                    appointment = Appointment(
                        professional_id=professional_id,
                        patient_id=patient_id,
                        appointment_date=appointment_date,
                        start_time=start_time,
                        end_time=end_time,
                        status='scheduled',
                        notes=notes,
                        meeting_link=meet_link
                    )
                    appointment.save()

                    flash('Consulta agendada com sucesso! Um link do Google Meet foi gerado para esta consulta.', 'success')
                else:
                    # Se falhar, criar o agendamento com um link fictício
                    import hashlib
                    import uuid
                    meeting_id = hashlib.md5(f"{professional_id}-{patient_id}-{appointment_date}-{start_time}-{uuid.uuid4()}".encode()).hexdigest()[:10]
                    fallback_link = f"https://meet.google.com/{meeting_id}-{appointment_date.replace('-', '')}"

                    appointment = Appointment(
                        professional_id=professional_id,
                        patient_id=patient_id,
                        appointment_date=appointment_date,
                        start_time=start_time,
                        end_time=end_time,
                        status='scheduled',
                        notes=notes,
                        meeting_link=fallback_link
                    )
                    appointment.save()

                    flash('Consulta agendada com sucesso! Um link do Google Meet fictício foi gerado para esta consulta.', 'warning')

            # Limpar as informações da sessão
            session.pop('appointment_form_data', None)
            session.pop('appointment_info', None)
            session.pop('needs_google_auth', None)
            session.pop('auth_redirect_url', None)

            # Redirecionar para a página de agendamentos
            return redirect(url_for('main.professional_appointments'))
        except Exception as e:
            flash(f'Erro ao criar evento no Google Calendar: {e}', 'error')

    # Verificar se há informações de agendamento existente na sessão
    appointment_info = session.get('appointment_info')
    if appointment_info:
        try:
            from app.google_calendar import create_google_meet_event
            from datetime import datetime, timedelta

            # Recuperar informações do agendamento
            professional_id = appointment_info.get('professional_id')
            patient_id = appointment_info.get('patient_id')
            appointment_date = appointment_info.get('appointment_date')
            start_time = appointment_info.get('start_time')
            summary = appointment_info.get('summary')
            description = appointment_info.get('description')
            attendees = appointment_info.get('attendees')

            # Converter a data e hora para objetos datetime
            appointment_datetime = datetime.strptime(f"{appointment_date} {start_time}", "%Y-%m-%d %H:%M")

            # Definir o horário de término (1 hora após o início)
            end_datetime = appointment_datetime + timedelta(hours=1)

            # Criar o evento no Google Calendar
            meet_link = create_google_meet_event(
                summary=summary,
                description=description,
                start_time=appointment_datetime,
                end_time=end_datetime,
                attendees=attendees
            )

            if meet_link:
                # Atualizar o link do Google Meet no agendamento
                conn = get_db()
                cursor = conn.cursor()
                cursor.execute(
                    'UPDATE appointment SET meeting_link = ? WHERE professional_id = ? AND patient_id = ? AND appointment_date = ? AND start_time = ?',
                    (meet_link, professional_id, patient_id, appointment_date, start_time)
                )
                conn.commit()
                conn.close()

                flash('Link do Google Meet criado com sucesso!', 'success')

            # Limpar as informações do agendamento da sessão
            session.pop('appointment_info', None)
            session.pop('needs_google_auth', None)
            session.pop('auth_redirect_url', None)
        except Exception as e:
            flash(f'Erro ao criar evento no Google Calendar: {e}', 'error')

    return result

@main.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data, role=form.role.data)
        user.set_password(form.password.data)
        user.save()
        flash('Congratulations, you are now a registered user!')
        return redirect(url_for('main.login'))
    return render_template('register.html', title='Register', form=form)

@main.route('/admin/dashboard')
@login_required
def admin_dashboard():
    if not current_user.is_admin():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professionals = Professional.get_all()
    return render_template('admin/dashboard.html', title='Admin Dashboard', professionals=professionals)

@main.route('/admin/add_professional', methods=['GET', 'POST'])
@login_required
def add_professional():
    if not current_user.is_admin():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    form = ProfessionalForm()
    if form.validate_on_submit():
        # Create a new user for the professional
        username = form.name.data.lower().replace(' ', '_')
        email = f"{username}@example.com"  # This should be changed in a real application
        password = "changeme"  # This should be changed in a real application

        user = User(username=username, email=email, role='professional')
        user.set_password(password)
        user.save()

        # Create the professional record
        professional = Professional(
            user_id=user.id,
            name=form.name.data,
            specialty=form.specialty.data,
            license_number=form.license_number.data,
            phone=form.phone.data
        )
        professional.save()

        flash(f'Professional {form.name.data} has been added!')
        return redirect(url_for('main.admin_dashboard'))
    return render_template('admin/add_professional.html', title='Add Professional', form=form)

@main.route('/admin/edit_professional/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_professional(id):
    if not current_user.is_admin():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_id(id)
    if professional is None:
        abort(404)
    form = ProfessionalForm(obj=professional)
    if form.validate_on_submit():
        professional.name = form.name.data
        professional.specialty = form.specialty.data
        professional.license_number = form.license_number.data
        professional.phone = form.phone.data
        professional.save()
        flash('Professional information has been updated!')
        return redirect(url_for('main.admin_dashboard'))
    return render_template('admin/edit_professional.html', title='Edit Professional', form=form, professional=professional)

@main.route('/admin/delete_professional/<int:id>')
@login_required
def delete_professional(id):
    if not current_user.is_admin():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_id(id)
    if professional is None:
        abort(404)
    user = User.get_by_id(professional.user_id)

    # Delete the professional (this will also delete associated patients)
    professional.delete()

    # Delete the user
    if user:
        user.delete()

    flash('Professional has been deleted!')
    return redirect(url_for('main.admin_dashboard'))

@main.route('/professional/dashboard')
@login_required
def professional_dashboard():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)
    patients = professional.get_patients()
    return render_template('professional/dashboard.html', title='Professional Dashboard',
                           professional=professional, patients=patients)


@main.route('/professional/patient/<int:id>')
@login_required
def view_patient(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    patient = Patient.get_by_id(id)
    if patient is None or patient.professional_id != professional.id:
        abort(404)

    # Obter todas as consultas deste paciente com este profissional
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute(
        'SELECT * FROM appointment WHERE professional_id = ? AND patient_id = ? ORDER BY appointment_date DESC, start_time DESC',
        (professional.id, patient.id)
    )
    appointment_data_list = cursor.fetchall()
    conn.close()

    appointments = []
    for appointment_data in appointment_data_list:
        appointments.append(Appointment(
            id=appointment_data['id'],
            professional_id=appointment_data['professional_id'],
            patient_id=appointment_data['patient_id'],
            appointment_date=appointment_data['appointment_date'],
            start_time=appointment_data['start_time'],
            end_time=appointment_data['end_time'],
            status=appointment_data['status'],
            notes=appointment_data['notes'],
            meeting_link=appointment_data['meeting_link'],
            post_appointment_notes=appointment_data['post_appointment_notes'],
            created_at=appointment_data['created_at']
        ))

    return render_template('professional/patient_detail.html', title=f'Paciente: {patient.name}',
                           patient=patient, appointments=appointments)


@main.route('/professional/consultations')
@login_required
def professional_consultations():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    # Obter todas as consultas concluídas deste profissional
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute(
        'SELECT * FROM appointment WHERE professional_id = ? AND status = "completed" ORDER BY appointment_date DESC, start_time DESC',
        (professional.id,)
    )
    appointment_data_list = cursor.fetchall()
    conn.close()

    # Criar lista de consultas
    consultations = []
    for appointment_data in appointment_data_list:
        appointment = Appointment(
            id=appointment_data['id'],
            professional_id=appointment_data['professional_id'],
            patient_id=appointment_data['patient_id'],
            appointment_date=appointment_data['appointment_date'],
            start_time=appointment_data['start_time'],
            end_time=appointment_data['end_time'],
            status=appointment_data['status'],
            notes=appointment_data['notes'],
            meeting_link=appointment_data['meeting_link'],
            post_appointment_notes=appointment_data['post_appointment_notes'],
            created_at=appointment_data['created_at']
        )
        # Obter dados do paciente
        patient = Patient.get_by_id(appointment.patient_id)
        consultations.append({
            'appointment': appointment,
            'patient': patient
        })

    return render_template('professional/consultations.html', title='Consultas Realizadas',
                           professional=professional, consultations=consultations)

@main.route('/professional/add_patient', methods=['GET', 'POST'])
@login_required
def add_patient():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)
    form = PatientForm()
    if form.validate_on_submit():
        patient = Patient(
            professional_id=professional.id,
            name=form.name.data,
            email=form.email.data,
            phone=form.phone.data,
            date_of_birth=form.date_of_birth.data,
            address=form.address.data,
            notes=form.notes.data
        )
        patient.save()
        flash(f'Patient {form.name.data} has been added!')
        return redirect(url_for('main.professional_dashboard'))
    return render_template('professional/add_patient.html', title='Add Patient', form=form)

@main.route('/professional/edit_patient/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_patient(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)
    patient = Patient.get_by_id(id)
    if patient is None or patient.professional_id != professional.id:
        abort(404)
    form = PatientForm(obj=patient)
    if form.validate_on_submit():
        patient.name = form.name.data
        patient.email = form.email.data
        patient.phone = form.phone.data
        patient.date_of_birth = form.date_of_birth.data
        patient.address = form.address.data
        patient.notes = form.notes.data
        patient.save()
        flash('Informações do paciente atualizadas com sucesso!')
        return redirect(url_for('main.view_patient', id=patient.id))
    return render_template('professional/edit_patient.html', title='Edit Patient', form=form, patient=patient)

@main.route('/professional/delete_patient/<int:id>')
@login_required
def delete_patient(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)
    patient = Patient.get_by_id(id)
    if patient is None or patient.professional_id != professional.id:
        abort(404)
    patient.delete()
    flash('Patient has been deleted!')
    return redirect(url_for('main.professional_dashboard'))


# Rotas para gerenciamento de agenda

@main.route('/professional/schedule')
@login_required
def professional_schedule():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    schedules = Schedule.get_by_professional_id(professional.id)

    # Agrupar horários por dia da semana
    schedule_by_day = {day: [] for day in range(7)}
    for schedule in schedules:
        schedule_by_day[schedule.day_of_week].append(schedule)

    return render_template('professional/schedule.html', title='My Schedule',
                           professional=professional, schedule_by_day=schedule_by_day)


@main.route('/professional/add_schedule', methods=['GET', 'POST'])
@login_required
def add_schedule():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    form = ScheduleForm()
    if form.validate_on_submit():
        schedule = Schedule(
            professional_id=professional.id,
            day_of_week=form.day_of_week.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data
        )
        schedule.save()
        flash('Schedule has been added!')
        return redirect(url_for('main.professional_schedule'))

    return render_template('professional/add_schedule.html', title='Add Schedule', form=form)


@main.route('/professional/edit_schedule/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_schedule(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    schedule = Schedule.get_by_id(id)
    if schedule is None or schedule.professional_id != professional.id:
        abort(404)

    form = ScheduleForm(obj=schedule)
    if form.validate_on_submit():
        schedule.day_of_week = form.day_of_week.data
        schedule.start_time = form.start_time.data
        schedule.end_time = form.end_time.data
        schedule.save()
        flash('Schedule has been updated!')
        return redirect(url_for('main.professional_schedule'))

    return render_template('professional/edit_schedule.html', title='Edit Schedule', form=form, schedule=schedule)


@main.route('/professional/delete_schedule/<int:id>')
@login_required
def delete_schedule(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    schedule = Schedule.get_by_id(id)
    if schedule is None or schedule.professional_id != professional.id:
        abort(404)

    schedule.delete()
    flash('Schedule has been deleted!')
    return redirect(url_for('main.professional_schedule'))


# Rotas para gerenciamento de agendamentos

@main.route('/professional/appointments', methods=['GET', 'POST'])
@login_required
def professional_appointments():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    # Obter pacientes do profissional para o dropdown
    patients = Patient.get_by_professional_id(professional.id)
    patient_choices = [(0, 'Todos os Pacientes')] + [(p.id, p.name) for p in patients]

    # Criar formulário de filtro
    filter_form = AppointmentFilterForm()
    filter_form.patient_id.choices = patient_choices

    # Obter data atual e calcular início e fim da semana
    today = date.today()
    start_of_week = today - timedelta(days=today.weekday())
    end_of_week = start_of_week + timedelta(days=6)

    # Definir valores padrão para o formulário
    if not filter_form.start_date.data:
        filter_form.start_date.data = start_of_week
    if not filter_form.end_date.data:
        filter_form.end_date.data = end_of_week

    # Processar o formulário de filtro
    start_date = None
    end_date = None
    patient_id = None
    status_filter = 'all'

    if filter_form.validate_on_submit():
        start_date = filter_form.start_date.data.strftime('%Y-%m-%d') if filter_form.start_date.data else None
        end_date = filter_form.end_date.data.strftime('%Y-%m-%d') if filter_form.end_date.data else None
        patient_id = filter_form.patient_id.data if filter_form.patient_id.data != 0 else None
        status_filter = filter_form.status.data
    else:
        # Valores padrão para a primeira carga da página
        start_date = start_of_week.strftime('%Y-%m-%d')
        end_date = end_of_week.strftime('%Y-%m-%d')

    # Obter agendamentos com base nos filtros
    appointments = Appointment.get_by_professional_id(
        professional.id,
        start_date=start_date,
        end_date=end_date
    )

    # Filtrar por paciente se necessário
    if patient_id:
        appointments = [a for a in appointments if a.patient_id == patient_id]

    # Filtrar por status se necessário
    if status_filter != 'all':
        appointments = [a for a in appointments if a.status == status_filter]

    # Agrupar agendamentos por data
    appointments_by_date = {}
    for appointment in appointments:
        date_str = appointment.appointment_date
        if date_str not in appointments_by_date:
            appointments_by_date[date_str] = []
        appointments_by_date[date_str].append(appointment)

    return render_template('professional/appointments.html', title='Meus Agendamentos',
                           professional=professional, appointments_by_date=appointments_by_date,
                           start_of_week=start_of_week, end_of_week=end_of_week,
                           filter_form=filter_form)


@main.route('/professional/add_appointment', methods=['GET', 'POST'])
@login_required
def add_appointment():
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    # Obter pacientes do profissional para o dropdown
    patients = Patient.get_by_professional_id(professional.id)
    if not patients:
        flash('You need to add patients before scheduling appointments.')
        return redirect(url_for('main.add_patient'))

    form = AppointmentForm()
    form.patient_id.choices = [(p.id, p.name) for p in patients]

    # Formulário de pesquisa para encontrar horários disponíveis
    search_form = AppointmentSearchForm()

    available_slots = []
    selected_date = None

    if search_form.validate_on_submit() and search_form.submit.data:
        selected_date = search_form.date.data.strftime('%Y-%m-%d')
        available_slots = Appointment.get_available_slots(professional.id, selected_date)

        if not available_slots:
            flash(f'No available slots found for {selected_date}. Please add schedule for this day or select another date.')

    if form.validate_on_submit() and form.submit.data:
        # Gerar link do Google Meet
        meeting_link = Appointment.generate_meet_link(
            professional.id,
            form.patient_id.data,
            form.appointment_date.data.strftime('%Y-%m-%d'),
            form.start_time.data
        )

        # Verificar se precisamos autenticar com o Google
        if meeting_link == "pending_google_auth" and session.get('needs_google_auth') and session.get('auth_redirect_url'):
            # Salvar os dados do formulário na sessão para uso após a autenticação
            session['appointment_form_data'] = {
                'professional_id': professional.id,
                'patient_id': form.patient_id.data,
                'appointment_date': form.appointment_date.data.strftime('%Y-%m-%d'),
                'start_time': form.start_time.data,
                'end_time': form.end_time.data,
                'notes': form.notes.data
            }

            # Redirecionar para a autenticação do Google
            return redirect(session['auth_redirect_url'])

        # Se não precisamos autenticar, criar o agendamento normalmente
        appointment = Appointment(
            professional_id=professional.id,
            patient_id=form.patient_id.data,
            appointment_date=form.appointment_date.data.strftime('%Y-%m-%d'),
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            status='scheduled',
            notes=form.notes.data,
            meeting_link=meeting_link
        )
        appointment.save()
        flash('Consulta agendada com sucesso! Um link do Google Meet foi gerado para esta consulta.')
        return redirect(url_for('main.professional_appointments'))

    return render_template('professional/add_appointment.html', title='Schedule Appointment',
                           form=form, search_form=search_form, available_slots=available_slots,
                           selected_date=selected_date)


@main.route('/professional/edit_appointment/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_appointment(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    appointment = Appointment.get_by_id(id)
    if appointment is None or appointment.professional_id != professional.id:
        abort(404)

    # Obter pacientes do profissional para o dropdown
    patients = Patient.get_by_professional_id(professional.id)

    form = AppointmentForm(obj=appointment)
    form.patient_id.choices = [(p.id, p.name) for p in patients]

    if form.validate_on_submit():
        appointment.patient_id = form.patient_id.data
        appointment.appointment_date = form.appointment_date.data.strftime('%Y-%m-%d')
        appointment.start_time = form.start_time.data
        appointment.end_time = form.end_time.data
        appointment.notes = form.notes.data
        appointment.save()
        flash('Appointment has been updated!')
        return redirect(url_for('main.professional_appointments'))

    return render_template('professional/edit_appointment.html', title='Edit Appointment',
                           form=form, appointment=appointment)


@main.route('/professional/cancel_appointment/<int:id>')
@login_required
def cancel_appointment(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    appointment = Appointment.get_by_id(id)
    if appointment is None or appointment.professional_id != professional.id:
        abort(404)

    appointment.status = 'cancelled'
    appointment.save()
    flash('Appointment has been cancelled!')
    return redirect(url_for('main.professional_appointments'))


@main.route('/professional/complete_appointment/<int:id>')
@login_required
def complete_appointment(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))
    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    appointment = Appointment.get_by_id(id)
    if appointment is None or appointment.professional_id != professional.id:
        abort(404)

    appointment.status = 'completed'
    appointment.save()
    flash('Consulta marcada como concluída! Você pode adicionar observações sobre a consulta.')
    return redirect(url_for('main.add_post_appointment_notes', id=appointment.id))


@main.route('/professional/add_post_appointment_notes/<int:id>', methods=['GET', 'POST'])
@login_required
def add_post_appointment_notes(id):
    if not current_user.is_professional():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))

    professional = Professional.get_by_user_id(current_user.id)
    if professional is None:
        abort(404)

    appointment = Appointment.get_by_id(id)
    if appointment is None or appointment.professional_id != professional.id:
        abort(404)

    # Verificar se a consulta está concluída
    if appointment.status != 'completed':
        flash('Você só pode adicionar observações para consultas concluídas.')
        return redirect(url_for('main.professional_appointments'))

    form = PostAppointmentNotesForm()

    if form.validate_on_submit():
        appointment.post_appointment_notes = form.post_appointment_notes.data
        appointment.save()
        flash('Observações da consulta salvas com sucesso!')
        return redirect(url_for('main.professional_appointments'))

    # Preencher o formulário com as observações existentes, se houver
    if appointment.post_appointment_notes:
        form.post_appointment_notes.data = appointment.post_appointment_notes

    patient = appointment.get_patient()

    return render_template('professional/post_appointment_notes.html',
                           title='Adicionar Observações da Consulta',
                           form=form, appointment=appointment, patient=patient)


# Rotas para pacientes

@main.route('/patient/register', methods=['GET', 'POST'])
def patient_register():
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = PatientRegistrationForm()
    if form.validate_on_submit():
        # Criar usuário
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='patient'
        )
        user.set_password(form.password.data)
        user.save()

        # Criar perfil de paciente
        patient = Patient(
            name=form.name.data,
            email=form.email.data,
            phone=form.phone.data,
            date_of_birth=form.date_of_birth.data,
            address=form.address.data,
            user_id=user.id
        )
        patient.save()

        flash('Registro concluído com sucesso! Agora você pode fazer login.')
        return redirect(url_for('main.login'))

    return render_template('patient/register.html', title='Patient Registration', form=form)


@main.route('/patient/dashboard')
@login_required
def patient_dashboard():
    if not current_user.is_patient():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))

    patient = Patient.get_by_user_id(current_user.id)
    if patient is None:
        abort(404)

    # Obter agendamentos do paciente
    appointments = Appointment.get_by_patient_id(patient.id)

    # Filtrar agendamentos futuros e passados
    today = date.today().strftime('%Y-%m-%d')
    upcoming_appointments = []
    past_appointments = []

    for appointment in appointments:
        if appointment.appointment_date >= today and appointment.status != 'cancelled':
            upcoming_appointments.append(appointment)
        else:
            past_appointments.append(appointment)

    return render_template('patient/dashboard.html', title='Patient Dashboard',
                           patient=patient, upcoming_appointments=upcoming_appointments,
                           past_appointments=past_appointments)


@main.route('/patient/view_professionals')
def view_professionals():
    professionals = Professional.get_all()
    return render_template('patient/view_professionals.html', title='View Professionals',
                           professionals=professionals)


@main.route('/patient/professional_schedule/<int:id>', methods=['GET', 'POST'])
def patient_view_professional_schedule(id):
    professional = Professional.get_by_id(id)
    if professional is None:
        abort(404)

    schedules = Schedule.get_by_professional_id(professional.id)

    # Agrupar horários por dia da semana
    schedule_by_day = {day: [] for day in range(7)}
    for schedule in schedules:
        schedule_by_day[schedule.day_of_week].append(schedule)

    # Obter a data atual para limitar o calendário
    today = date.today()
    today_date = today.strftime('%Y-%m-%d')

    # Formulário de pesquisa para encontrar horários disponíveis
    form = AppointmentSearchForm()

    available_slots = []
    selected_date = None

    # Processar o formulário de pesquisa
    if form.validate_on_submit():
        selected_date = form.date.data.strftime('%Y-%m-%d')

        # Verificar se a data selecionada não é anterior à data atual
        if form.date.data < today:
            flash('Não é possível agendar consultas em datas passadas. Por favor, selecione uma data futura.')
        else:
            available_slots = Appointment.get_available_slots(professional.id, selected_date)

            if not available_slots:
                flash(f'Não há horários disponíveis para {selected_date}. Por favor, selecione outra data.')

    return render_template('patient/professional_schedule.html', title=f'{professional.name} Schedule',
                           professional=professional, schedule_by_day=schedule_by_day,
                           form=form, available_slots=available_slots, selected_date=selected_date,
                           today_date=today_date)


@main.route('/patient/book_appointment/<int:professional_id>', methods=['GET', 'POST'])
@login_required
def book_appointment(professional_id):
    if not current_user.is_patient():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))

    patient = Patient.get_by_user_id(current_user.id)
    if patient is None:
        abort(404)

    professional = Professional.get_by_id(professional_id)
    if professional is None:
        abort(404)

    form = PatientAppointmentForm()

    if form.validate_on_submit():
        # Gerar link do Google Meet
        meeting_link = Appointment.generate_meet_link(
            professional.id,
            patient.id,
            form.appointment_date.data,
            form.start_time.data
        )

        # Verificar se precisamos autenticar com o Google
        if meeting_link == "pending_google_auth" and session.get('needs_google_auth') and session.get('auth_redirect_url'):
            # Salvar os dados do formulário na sessão para uso após a autenticação
            session['appointment_form_data'] = {
                'professional_id': professional.id,
                'patient_id': patient.id,
                'appointment_date': form.appointment_date.data,
                'start_time': form.start_time.data,
                'end_time': form.end_time.data,
                'notes': form.notes.data
            }

            # Redirecionar para a autenticação do Google
            return redirect(session['auth_redirect_url'])

        # Se não precisamos autenticar, criar o agendamento normalmente
        appointment = Appointment(
            professional_id=professional.id,
            patient_id=patient.id,
            appointment_date=form.appointment_date.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            status='scheduled',
            notes=form.notes.data,
            meeting_link=meeting_link
        )
        appointment.save()
        flash('Consulta agendada com sucesso! Um link do Google Meet foi gerado para sua consulta.')
        return redirect(url_for('main.patient_dashboard'))

    # Obter data e horário da query string
    date_str = request.args.get('date')
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')

    if date_str and start_time and end_time:
        form.appointment_date.data = date_str
        form.start_time.data = start_time
        form.end_time.data = end_time
    else:
        flash('Informações de agendamento incompletas.')
        return redirect(url_for('main.patient_view_professional_schedule', id=professional.id))

    return render_template('patient/book_appointment.html', title='Book Appointment',
                           form=form, professional=professional,
                           date_str=date_str, time_slot=f"{start_time} - {end_time}")


@main.route('/patient/cancel_appointment/<int:id>')
@login_required
def patient_cancel_appointment(id):
    if not current_user.is_patient():
        flash('You do not have permission to access this page.')
        return redirect(url_for('main.index'))

    patient = Patient.get_by_user_id(current_user.id)
    if patient is None:
        abort(404)

    appointment = Appointment.get_by_id(id)
    if appointment is None or appointment.patient_id != patient.id:
        abort(404)

    appointment.status = 'cancelled'
    appointment.save()
    flash('Agendamento cancelado com sucesso!')
    return redirect(url_for('main.patient_dashboard'))
